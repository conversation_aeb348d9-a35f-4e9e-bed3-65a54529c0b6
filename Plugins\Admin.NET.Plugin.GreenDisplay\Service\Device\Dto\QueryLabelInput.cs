// Admin.NET 项目的版权、商标、专利和其他相关权利均受相应法律法规的保护。使用本项目应遵守相关法律法规和许可证的要求。
//
// 本项目主要遵循 MIT 许可证和 Apache 许可证（版本 2.0）进行分发和使用。许可证位于源代码树根目录中的 LICENSE-MIT 和 LICENSE-APACHE 文件。
//
// 不得利用本项目从事危害国家安全、扰乱社会秩序、侵犯他人合法权益等法律法规禁止的活动！任何基于本项目二次开发而产生的一切法律纠纷和责任，我们不承担任何责任！

using System.ComponentModel.DataAnnotations;

namespace Admin.NET.Plugin.GreenDisplay.Service;

/// <summary>
/// 查询标签输入
/// </summary>
public class QueryLabelInput
{
    /// <summary>
    /// 页码
    /// </summary>
    public int PageNo { get; set; } = 1;

    /// <summary>
    /// 每页记录数
    /// </summary>
    public int PageSize { get; set; } = 100;

    /// <summary>
    /// 桌牌MAC地址
    /// </summary>
    public string LabelMac { get; set; }

    /// <summary>
    /// 房间ID
    /// </summary>
    public string RoomId { get; set; }

    /// <summary>
    /// 人员编号
    /// </summary>
    public string StaffCode { get; set; }

    /// <summary>
    /// 状态
    /// </summary>
    public string Status { get; set; }

    /// <summary>
    /// 结果
    /// </summary>
    public string Result { get; set; }

    /// <summary>
    /// 座位
    /// </summary>
    public string Remark { get; set; }
}

/// <summary>
/// 更新标签输入
/// </summary>
public class UpdateLabelInput
{
    /// <summary>
    /// 桌牌MAC地址
    /// </summary>
    [Required(ErrorMessage = "桌牌MAC地址不能为空")]
    public string LabelMac { get; set; }

    /// <summary>
    /// 会议室编号
    /// </summary>
    public string RoomId { get; set; }

    /// <summary>
    /// 座位
    /// </summary>
    public string Remark { get; set; }
}