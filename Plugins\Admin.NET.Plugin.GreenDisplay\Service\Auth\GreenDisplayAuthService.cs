// Admin.NET 项目的版权、商标、专利和其他相关权利均受相应法律法规的保护。使用本项目应遵守相关法律法规和许可证的要求。
//
// 本项目主要遵循 MIT 许可证和 Apache 许可证（版本 2.0）进行分发和使用。许可证位于源代码树根目录中的 LICENSE-MIT 和 LICENSE-APACHE 文件。
//
// 不得利用本项目从事危害国家安全、扰乱社会秩序、侵犯他人合法权益等法律法规禁止的活动！任何基于本项目二次开发而产生的一切法律纠纷和责任，我们不承担任何责任！

using Admin.NET.Plugin.GreenDisplay.Const;

namespace Admin.NET.Plugin.GreenDisplay.Service;

/// <summary>
/// 认证服务 🧩
/// </summary>
[UnifyProvider("GreenDisplay")]
public class GreenDisplayAuthService
{
    private readonly SysAuthService _sysAuthService;
    private readonly SqlSugarRepository<SysUser> _sysUserRep;
    private readonly SysCacheService _sysCacheService;

    public GreenDisplayAuthService(SysAuthService sysAuthService,
        SqlSugarRepository<SysUser> sysUserRep,
        SysCacheService sysCacheService)
    {
        _sysAuthService = sysAuthService;
        _sysUserRep = sysUserRep;
        _sysCacheService = sysCacheService;
    }

    /// <summary>
    /// 用户登录并获取访问令牌 🔖
    /// </summary>
    /// <param name="input">登录信息</param>
    /// <returns>访问令牌</returns>
    public async Task<GreenDisplayLoginOutput> Login(GreenDisplayLoginInput input)
    {
        // 设置默认租户
        var tenantId = SqlSugarConst.DefaultTenantId;

        // 临时禁用验证码
        _sysCacheService.Set($"{CacheConst.KeyConfig}{ConfigConst.SysCaptcha}", false);

        try
        {
            // 加密密码
            var encryptedPassword = CryptogramUtil.SM2Encrypt(input.Password);
            
            // 调用系统登录服务
            var loginResult = await _sysAuthService.Login(new LoginInput()
            {
                Account = input.Username,
                Password = encryptedPassword,
                TenantId = tenantId
            });

            // 返回访问令牌
            return new GreenDisplayLoginOutput()
            {
                AccessToken = loginResult.AccessToken
            };
        }
        finally
        {
            // 恢复验证码设置
            _sysCacheService.Remove($"{CacheConst.KeyConfig}{ConfigConst.SysCaptcha}");
        }
    }
}