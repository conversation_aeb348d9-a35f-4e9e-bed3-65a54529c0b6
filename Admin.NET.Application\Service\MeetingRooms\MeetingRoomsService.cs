﻿// Admin.NET 项目的版权、商标、专利和其他相关权利均受相应法律法规的保护。使用本项目应遵守相关法律法规和许可证的要求。
//
// 本项目主要遵循 MIT 许可证和 Apache 许可证（版本 2.0）进行分发和使用。许可证位于源代码树根目录中的 LICENSE-MIT 和 LICENSE-APACHE 文件。
//
// 不得利用本项目从事危害国家安全、扰乱社会秩序、侵犯他人合法权益等法律法规禁止的活动！任何基于本项目二次开发而产生的一切法律纠纷和责任，我们不承担任何责任！

using Admin.NET.Core.Service;
using Microsoft.AspNetCore.Http;
using Furion.DatabaseAccessor;
using Furion.FriendlyException;
using Mapster;
using SqlSugar;
using System.ComponentModel;
using System.ComponentModel.DataAnnotations;
using Admin.NET.Application.Entity;
namespace Admin.NET.Application;

/// <summary>
/// 会议室表服务 🧩
/// </summary>
[ApiDescriptionSettings(ApplicationConst.GroupName, Order = 100)]
public partial class MeetingRoomsService : IDynamicApiController, ITransient
{
    private readonly SqlSugarRepository<MeetingRooms> _meetingRoomsRep;

    public MeetingRoomsService(SqlSugarRepository<MeetingRooms> meetingRoomsRep)
    {
        _meetingRoomsRep = meetingRoomsRep;
    }

    /// <summary>
    /// 分页查询会议室表 🔖
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [DisplayName("分页查询会议室表")]
    [ApiDescriptionSettings(Name = "Page"), HttpPost]
    public async Task<SqlSugarPagedList<MeetingRoomsOutput>> Page(PageMeetingRoomsInput input)
    {
        input.Keyword = input.Keyword?.Trim();
        var query = _meetingRoomsRep.AsQueryable()
            .WhereIF(!string.IsNullOrWhiteSpace(input.Keyword), u => u.room_name.Contains(input.Keyword) || u.room_code.Contains(input.Keyword) || u.room_location.Contains(input.Keyword))
            .WhereIF(!string.IsNullOrWhiteSpace(input.room_name), u => u.room_name.Contains(input.room_name.Trim()))
            .WhereIF(!string.IsNullOrWhiteSpace(input.room_code), u => u.room_code.Contains(input.room_code.Trim()))
            .WhereIF(!string.IsNullOrWhiteSpace(input.room_location), u => u.room_location.Contains(input.room_location.Trim())) 
            .WhereIF(input.capacity != null, u => u.capacity == input.capacity)
            .WhereIF(input.meeting_status != null, u => u.meeting_status == input.meeting_status)
            .Select<MeetingRoomsOutput>();
		return await query.OrderBuilder(input).ToPagedListAsync(input.Page, input.PageSize);
    }

    /// <summary>
    /// 获取会议室表详情 ℹ️
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [DisplayName("获取会议室表详情")]
    [ApiDescriptionSettings(Name = "Detail"), HttpGet]
    public async Task<MeetingRooms> Detail([FromQuery] QueryByIdMeetingRoomsInput input)
    {
        return await _meetingRoomsRep.GetFirstAsync(u => u.Id == input.Id);
    }

    /// <summary>
    /// 增加会议室表 ➕
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [DisplayName("增加会议室表")]
    [ApiDescriptionSettings(Name = "Add"), HttpPost]
    public async Task<long> Add(AddMeetingRoomsInput input)
    {
        var entity = input.Adapt<MeetingRooms>();
        return await _meetingRoomsRep.InsertAsync(entity) ? entity.Id : 0;
    }

    /// <summary>
    /// 更新会议室表 ✏️
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [DisplayName("更新会议室表")]
    [ApiDescriptionSettings(Name = "Update"), HttpPost]
    public async Task Update(UpdateMeetingRoomsInput input)
    {
        var entity = input.Adapt<MeetingRooms>();
        await _meetingRoomsRep.AsUpdateable(entity)
        .ExecuteCommandAsync();
    }

    /// <summary>
    /// 删除会议室表 ❌
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [DisplayName("删除会议室表")]
    [ApiDescriptionSettings(Name = "Delete"), HttpPost]
    public async Task Delete(DeleteMeetingRoomsInput input)
    {
        var entity = await _meetingRoomsRep.GetFirstAsync(u => u.Id == input.Id) ?? throw Oops.Oh(ErrorCodeEnum.D1002);
        await _meetingRoomsRep.FakeDeleteAsync(entity);   //假删除
        //await _meetingRoomsRep.DeleteAsync(entity);   //真删除
    }

    /// <summary>
    /// 批量删除会议室表 ❌
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [DisplayName("批量删除会议室表")]
    [ApiDescriptionSettings(Name = "BatchDelete"), HttpPost]
    public async Task<int> BatchDelete([Required(ErrorMessage = "主键列表不能为空")]List<DeleteMeetingRoomsInput> input)
    {
        var exp = Expressionable.Create<MeetingRooms>();
        foreach (var row in input) exp = exp.Or(it => it.Id == row.Id);
        var list = await _meetingRoomsRep.AsQueryable().Where(exp.ToExpression()).ToListAsync();
   
        return await _meetingRoomsRep.FakeDeleteAsync(list);   //假删除
        //return await _meetingRoomsRep.DeleteAsync(list);   //真删除
    }
}
