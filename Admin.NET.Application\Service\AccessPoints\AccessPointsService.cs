﻿// Admin.NET 项目的版权、商标、专利和其他相关权利均受相应法律法规的保护。使用本项目应遵守相关法律法规和许可证的要求。
//
// 本项目主要遵循 MIT 许可证和 Apache 许可证（版本 2.0）进行分发和使用。许可证位于源代码树根目录中的 LICENSE-MIT 和 LICENSE-APACHE 文件。
//
// 不得利用本项目从事危害国家安全、扰乱社会秩序、侵犯他人合法权益等法律法规禁止的活动！任何基于本项目二次开发而产生的一切法律纠纷和责任，我们不承担任何责任！

using Admin.NET.Core.Service;
using Microsoft.AspNetCore.Http;
using Furion.DatabaseAccessor;
using Furion.FriendlyException;
using Mapster;
using SqlSugar;
using System.ComponentModel;
using System.ComponentModel.DataAnnotations;
using Admin.NET.Application.Entity;
namespace Admin.NET.Application;

/// <summary>
/// 接入点/网关表服务 🧩
/// </summary>
[ApiDescriptionSettings(ApplicationConst.GroupName, Order = 100)]
public partial class AccessPointsService : IDynamicApiController, ITransient
{
    private readonly SqlSugarRepository<AccessPoints> _accessPointsRep;

    public AccessPointsService(SqlSugarRepository<AccessPoints> accessPointsRep)
    {
        _accessPointsRep = accessPointsRep;
    }

    /// <summary>
    /// 分页查询接入点/网关表 🔖
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [DisplayName("分页查询接入点/网关表")]
    [ApiDescriptionSettings(Name = "Page"), HttpPost]
    public async Task<SqlSugarPagedList<AccessPointsOutput>> Page(PageAccessPointsInput input)
    {
        input.Keyword = input.Keyword?.Trim();
        var query = _accessPointsRep.AsQueryable()
            .WhereIF(!string.IsNullOrWhiteSpace(input.Keyword), u => u.ap_name.Contains(input.Keyword) || u.ap_location.Contains(input.Keyword))
            .WhereIF(!string.IsNullOrWhiteSpace(input.ap_name), u => u.ap_name.Contains(input.ap_name.Trim()))
            .WhereIF(!string.IsNullOrWhiteSpace(input.ap_location), u => u.ap_location.Contains(input.ap_location.Trim()))
            .WhereIF(input.ap_status != null, u => u.ap_status == input.ap_status)
            .Select<AccessPointsOutput>();
		return await query.OrderBuilder(input).ToPagedListAsync(input.Page, input.PageSize);
    }

    /// <summary>
    /// 获取接入点/网关表详情 ℹ️
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [DisplayName("获取接入点/网关表详情")]
    [ApiDescriptionSettings(Name = "Detail"), HttpGet]
    public async Task<AccessPoints> Detail([FromQuery] QueryByIdAccessPointsInput input)
    {
        return await _accessPointsRep.GetFirstAsync(u => u.Id == input.Id);
    }

    /// <summary>
    /// 增加接入点/网关表 ➕
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [DisplayName("增加接入点/网关表")]
    [ApiDescriptionSettings(Name = "Add"), HttpPost]
    public async Task<long> Add(AddAccessPointsInput input)
    {
        var entity = input.Adapt<AccessPoints>();
        return await _accessPointsRep.InsertAsync(entity) ? entity.Id : 0;
    }

    /// <summary>
    /// 更新接入点/网关表 ✏️
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [DisplayName("更新接入点/网关表")]
    [ApiDescriptionSettings(Name = "Update"), HttpPost]
    public async Task Update(UpdateAccessPointsInput input)
    {
        var entity = input.Adapt<AccessPoints>();
        await _accessPointsRep.AsUpdateable(entity)
        .ExecuteCommandAsync();
    }

    /// <summary>
    /// 删除接入点/网关表 ❌
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [DisplayName("删除接入点/网关表")]
    [ApiDescriptionSettings(Name = "Delete"), HttpPost]
    public async Task Delete(DeleteAccessPointsInput input)
    {
        var entity = await _accessPointsRep.GetFirstAsync(u => u.Id == input.Id) ?? throw Oops.Oh(ErrorCodeEnum.D1002);
        await _accessPointsRep.FakeDeleteAsync(entity);   //假删除
        //await _accessPointsRep.DeleteAsync(entity);   //真删除
    }

    /// <summary>
    /// 批量删除接入点/网关表 ❌
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [DisplayName("批量删除接入点/网关表")]
    [ApiDescriptionSettings(Name = "BatchDelete"), HttpPost]
    public async Task<int> BatchDelete([Required(ErrorMessage = "主键列表不能为空")]List<DeleteAccessPointsInput> input)
    {
        var exp = Expressionable.Create<AccessPoints>();
        foreach (var row in input) exp = exp.Or(it => it.Id == row.Id);
        var list = await _accessPointsRep.AsQueryable().Where(exp.ToExpression()).ToListAsync();
   
        return await _accessPointsRep.FakeDeleteAsync(list);   //假删除
        //return await _accessPointsRep.DeleteAsync(list);   //真删除
    }
}
