crit: 2025-07-09 10:30:41.3399762 +08:00 Wednesday L System.Logging.ScheduleService[0] #27
      [Furion.Pure.dll] void Furion.Schedule.ScheduleLogger.LogCritical(string message, params object[] args)
      Schedule hosted service is stopped.
crit: 2025-07-09 10:37:23.0368269 +08:00 Wednesday L System.Logging.ScheduleService[0] #10
      [Furion.Pure.dll] void Furion.Schedule.ScheduleLogger.LogCritical(string message, params object[] args)
      Schedule hosted service is stopped.
crit: 2025-07-09 10:56:40.5132219 +08:00 Wednesday L System.Logging.ScheduleService[0] #34
      [Furion.Pure.dll] void Furion.Schedule.ScheduleLogger.LogCritical(string message, params object[] args)
      Schedule hosted service is stopped.
crit: 2025-07-09 10:56:40.5370830 +08:00 Wednesday L System.Logging.TaskQueueService[0] #10
      [Furion.Pure.dll] async Task Furion.TaskQueue.TaskQueueHostedService.ExecuteAsync(CancellationToken stoppingToken)
      TaskQueue hosted service is stopped.
