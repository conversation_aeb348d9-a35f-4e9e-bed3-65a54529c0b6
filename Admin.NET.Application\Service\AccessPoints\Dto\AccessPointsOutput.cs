﻿// Admin.NET 项目的版权、商标、专利和其他相关权利均受相应法律法规的保护。使用本项目应遵守相关法律法规和许可证的要求。
//
// 本项目主要遵循 MIT 许可证和 Apache 许可证（版本 2.0）进行分发和使用。许可证位于源代码树根目录中的 LICENSE-MIT 和 LICENSE-APACHE 文件。
//
// 不得利用本项目从事危害国家安全、扰乱社会秩序、侵犯他人合法权益等法律法规禁止的活动！任何基于本项目二次开发而产生的一切法律纠纷和责任，我们不承担任何责任！
using Magicodes.ExporterAndImporter.Core;
namespace Admin.NET.Application;

/// <summary>
/// 接入点/网关表输出参数
/// </summary>
public class AccessPointsOutput
{
    /// <summary>
    /// 主键Id
    /// </summary>
    public long Id { get; set; }    
    
    /// <summary>
    /// AP名称
    /// </summary>
    public string? ap_name { get; set; }    
    
    /// <summary>
    /// 物理地址
    /// </summary>
    public string? mac_address { get; set; }    
    
    /// <summary>
    /// IP地址
    /// </summary>
    public string? ip_address { get; set; }    
    
    /// <summary>
    /// ap地址
    /// </summary>
    public string? ap_location { get; set; }    
    
    /// <summary>
    /// ap状态1：在线；2：离线；3：异常；
    /// </summary>
    public int? ap_status { get; set; }    
    
    /// <summary>
    /// 固件版本
    /// </summary>
    public string? firmware_version { get; set; }    
    
    /// <summary>
    /// 信号强度
    /// </summary>
    public int? signal_strength { get; set; }    
    
    /// <summary>
    /// 链接设备连接数
    /// </summary>
    public int? connected_devices_count { get; set; }    
    
    /// <summary>
    /// 最大链接数
    /// </summary>
    public int? max_devices { get; set; }    
    
    /// <summary>
    /// 软删除
    /// </summary>
    public bool IsDelete { get; set; }    
    
    /// <summary>
    /// 创建时间
    /// </summary>
    public DateTime? CreateTime { get; set; }    
    
    /// <summary>
    /// 更新时间
    /// </summary>
    public DateTime? UpdateTime { get; set; }    
    
    /// <summary>
    /// 创建者Id
    /// </summary>
    public long? CreateUserId { get; set; }    
    
    /// <summary>
    /// 创建者姓名
    /// </summary>
    public string? CreateUserName { get; set; }    
    
    /// <summary>
    /// 修改者Id
    /// </summary>
    public long? UpdateUserId { get; set; }    
    
    /// <summary>
    /// 修改者姓名
    /// </summary>
    public string? UpdateUserName { get; set; }    
    
}
