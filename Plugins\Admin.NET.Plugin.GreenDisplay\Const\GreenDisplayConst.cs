// Admin.NET 项目的版权、商标、专利和其他相关权利均受相应法律法规的保护。使用本项目应遵守相关法律法规和许可证的要求。
//
// 本项目主要遵循 MIT 许可证和 Apache 许可证（版本 2.0）进行分发和使用。许可证位于源代码树根目录中的 LICENSE-MIT 和 LICENSE-APACHE 文件。
//
// 不得利用本项目从事危害国家安全、扰乱社会秩序、侵犯他人合法权益等法律法规禁止的活动！任何基于本项目二次开发而产生的一切法律纠纷和责任，我们不承担任何责任！

using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using Admin.NET.Plugin.GreenDisplay.Option;
using Furion;

namespace Admin.NET.Plugin.GreenDisplay.Const;

/// <summary>
/// GreenDisplay 相关常量
/// </summary>
[Const("GreenDisplay 相关常量")]
public class GreenDisplayConst
{
    /// <summary>
    /// API分组名称
    /// </summary>
    public const string GroupName = "GreenDisplay";

    /// <summary>
    /// 第三方平台基础地址（编译时常量，用于特性）
    /// </summary>
    public const string ThirdPartyBaseUrl = "http://**************:19080";

    /// <summary>
    /// API路径前缀（编译时常量，用于特性）
    /// </summary>
    public const string ApiPrefix = "/admin-api/api";

    /// <summary>
    /// 获取运行时配置的第三方平台基础地址
    /// </summary>
    public static string GetThirdPartyBaseUrl() => App.GetConfig<ThirdPartyApiOptions>("[openapi:ThirdPartyApi]", true)?.BaseUrl ?? ThirdPartyBaseUrl;

    /// <summary>
    /// 获取运行时配置的API路径前缀
    /// </summary>
    public static string GetApiPrefix() => App.GetConfig<ThirdPartyApiOptions>("[openapi:ThirdPartyApi]", true)?.ApiPrefix ?? ApiPrefix;

    /// <summary>
    /// 获取完整的API基础URL（运行时配置）
    /// </summary>
    public static string GetApiBaseUrl() => $"{GetThirdPartyBaseUrl().TrimEnd('/')}{GetApiPrefix()}";

    /// <summary>
    /// 获取完整的API地址
    /// </summary>
    /// <param name="route">路由路径</param>
    /// <returns>完整的API地址</returns>
    public static string GetFullApiUrl(string route)
    {
        var options = App.GetConfig<ThirdPartyApiOptions>("[openapi:ThirdPartyApi]", true);
        return options?.GetFullUrl(route) ?? $"http://**************:19080/admin-api/api{route}";
    }

    /// <summary>
    /// API路由常量
    /// </summary>
    public static class ApiRoutes
{
    /// <summary>
    /// 认证相关路由
    /// </summary>
    public static class Auth
    {
        public const string Login = "/auth/login";
    }

    /// <summary>
    /// 标签相关路由
    /// </summary>
    public static class Label
    {
        public const string BindMultiple = "/label/bind-multiple";
    }

    /// <summary>
    /// 设备相关路由
    /// </summary>
    public static class Device
    {
        public const string BindDataMultiple = "/device/bind-data-multi";
        public const string FlashLabel = "/device/flash-label";
        public const string Get = "/device/get";
        public const string Query = "/device/query";
        public const string UpdateLabel = "/device/update-label";
    }

        /// <summary>
        /// 会议相关路由
        /// </summary>
        public static class Meeting
        {
            /// <summary>
            /// 会议室路由
            /// </summary>
            public static class Room
            {
                public const string Query = "/meeting/room/query";
                public const string Create = "/meeting/room/create";
                public const string Update = "/meeting/room/update";
                public const string Delete = "/meeting/room/delete";
            }

            /// <summary>
            /// 会议人员路由
            /// </summary>
            public static class Staff
            {
                public const string Query = "/meeting/staff/query";
                public const string Create = "/meeting/staff/create";
                public const string BatchCreate = "/meeting/staff/batch-create";
                public const string Update = "/meeting/staff/update";
                public const string BatchUpdate = "/meeting/staff/batch-update";
                public const string Delete = "/meeting/staff/delete";
            }
        }

        /// <summary>
        /// 模板相关路由
        /// </summary>
        public static class Template
        {
            public const string Query = "/template/query";
            public const string Create = "/template/create";
            public const string Design = "/template/design";
            public const string Update = "/template/update";
            public const string Delete = "/template/delete";
        }

        /// <summary>
        /// AP相关路由
        /// </summary>
        public static class AP
        {
            public const string List = "/ap/list";
            public const string Create = "/ap/create";
            public const string Get = "/ap/get";
            public const string Update = "/ap/update";
            public const string Delete = "/ap/delete";
        }
    }
}
