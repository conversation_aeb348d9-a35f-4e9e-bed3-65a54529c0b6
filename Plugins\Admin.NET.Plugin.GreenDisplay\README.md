# Admin.NET.Plugin.GreenDisplay

基于 Admin.NET 框架的 GreenDisplay 插件，提供电子桌牌系统的后端 API 支持。

## 功能特性

### 认证管理
- **用户登录**: 提供用户身份验证和访问令牌获取
- **密码加密**: 使用 SM2 算法对密码进行加密处理
- **统一返回格式**: 使用自定义的结果提供器统一 API 返回格式

### 标签管理
- **批量绑定标签**: 支持一次性绑定多个标签
- **绑定结果统计**: 返回成功和失败的绑定数量
- **详细错误信息**: 提供每个标签绑定的详细结果

## API 接口

### 认证接口

#### POST /admin-api/api/auth/login
用户登录接口

**请求参数:**
```json
{
  "username": "用户名",
  "password": "密码"
}
```

**返回结果:**
```json
{
  "code": 200,
  "type": "success",
  "message": "",
  "result": {
    "accessToken": "访问令牌"
  },
  "extras": null,
  "time": "2024-01-01T00:00:00"
}
```

### 标签管理接口

#### POST /admin-api/api/label/bind-multiple
批量绑定标签接口

**请求参数:**
```json
{
  "labelIds": ["标签ID1", "标签ID2", "标签ID3"],
  "bindingData": {
    "key": "value"
  }
}
```

**返回结果:**
```json
{
  "code": 200,
  "type": "success",
  "message": "",
  "result": {
    "successCount": 2,
    "failureCount": 1,
    "results": [
      {
        "labelId": "标签ID1",
        "success": true,
        "errorMessage": null
      },
      {
        "labelId": "标签ID2",
        "success": true,
        "errorMessage": null
      },
      {
        "labelId": "标签ID3",
        "success": false,
        "errorMessage": "绑定失败原因"
      }
    ]
  },
  "extras": null,
  "time": "2024-01-01T00:00:00"
}
```

## 项目结构

```
Admin.NET.Plugin.GreenDisplay/
├── Configuration/           # 配置文件
│   └── GreenDisplay.json   # OpenAPI 文档配置
├── Const/                  # 常量定义
│   └── GreenDisplayConst.cs
├── Controller/             # 控制器
│   └── GreenDisplayController.cs
├── Service/                # 服务层
│   ├── GreenDisplayAuthService.cs      # 认证服务
│   ├── GreenDisplayLabelService.cs     # 标签服务
│   ├── GreenDisplayLoginInput.cs       # 登录输入模型
│   ├── GreenDisplayLoginOutput.cs      # 登录输出模型
│   ├── BindMultipleLabelsInput.cs      # 批量绑定输入模型
│   └── BindMultipleLabelsOutput.cs     # 批量绑定输出模型
├── Util/                   # 工具类
│   └── GreenDisplayResultProvider.cs  # 结果提供器
├── GlobalUsings.cs         # 全局引用
└── Startup.cs             # 启动配置
```

## 技术特点

1. **统一结果格式**: 实现了 `IUnifyResultProvider` 接口，提供统一的 API 返回格式
2. **依赖注入**: 使用 .NET Core 的依赖注入容器管理服务生命周期
3. **安全加密**: 使用 SM2 算法对敏感数据进行加密处理
4. **模块化设计**: 采用插件化架构，便于扩展和维护
5. **OpenAPI 文档**: 自动生成 API 文档，便于接口调试和集成

## 部署说明

1. 确保项目已正确编译
2. 将插件 DLL 文件复制到主应用程序的插件目录
3. 重启应用程序以加载插件
4. 通过 Swagger 文档查看和测试 API 接口