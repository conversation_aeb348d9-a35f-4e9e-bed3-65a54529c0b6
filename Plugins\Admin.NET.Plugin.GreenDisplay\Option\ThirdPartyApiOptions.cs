// Admin.NET 项目的版权、商标、专利和其他相关权利均受相应法律法规的保护。使用本项目应遵守相关法律法规和许可证的要求。
//
// 本项目主要遵循 MIT 许可证和 Apache 许可证（版本 2.0）进行分发和使用。许可证位于源代码树根目录中的 LICENSE-MIT 和 LICENSE-APACHE 文件。
//
// 不得利用本项目从事危害国家安全、扰乱社会秩序、侵犯他人合法权益等法律法规禁止的活动！任何基于本项目二次开发而产生的一切法律纠纷和责任，我们不承担任何责任！

namespace Admin.NET.Plugin.GreenDisplay.Option;

/// <summary>
/// 第三方API配置选项
/// </summary>
public sealed class ThirdPartyApiOptions : IConfigurableOptions
{
    /// <summary>
    /// 基础URL
    /// </summary>
    public string BaseUrl { get; set; } = "http://**************:19080";

    /// <summary>
    /// API路径前缀
    /// </summary>
    public string ApiPrefix { get; set; } = "/admin-api/api";

    /// <summary>
    /// 超时时间（毫秒）
    /// </summary>
    public int Timeout { get; set; } = 30000;

    /// <summary>
    /// 重试次数
    /// </summary>
    public int RetryCount { get; set; } = 3;

    /// <summary>
    /// 是否启用日志记录
    /// </summary>
    public bool EnableLogging { get; set; } = true;

    /// <summary>
    /// 描述
    /// </summary>
    public string Description { get; set; } = "第三方平台API配置";

    /// <summary>
    /// 获取完整的API地址
    /// </summary>
    /// <param name="route">路由路径</param>
    /// <returns>完整的API地址</returns>
    public string GetFullUrl(string route)
    {
        return $"{BaseUrl.TrimEnd('/')}{ApiPrefix.TrimEnd('/')}{route}";
    }
}