// Admin.NET 项目的版权、商标、专利和其他相关权利均受相应法律法规的保护。使用本项目应遵守相关法律法规和许可证的要求。
//
// 本项目主要遵循 MIT 许可证和 Apache 许可证（版本 2.0）进行分发和使用。许可证位于源代码树根目录中的 LICENSE-MIT 和 LICENSE-APACHE 文件。
//
// 不得利用本项目从事危害国家安全、扰乱社会秩序、侵犯他人合法权益等法律法规禁止的活动！任何基于本项目二次开发而产生的一切法律纠纷和责任，我们不承担任何责任！

using Admin.NET.Plugin.GreenDisplay.Service;
using Admin.NET.Plugin.GreenDisplay.Const;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Logging;

namespace Admin.NET.Plugin.GreenDisplay.Controller;

/// <summary>
/// 绿色显示控制器 🎮
/// </summary>
[ApiDescriptionSettings(GreenDisplayConst.GroupName, Order = 100)]
public class GreenDisplayController : IDynamicApiController
{
    private readonly GreenDisplayAuthService _authService;
    private readonly GreenDisplayLabelService _labelService;
    private readonly GreenDisplayDeviceService _deviceService;
    private readonly GreenDisplayMeetingService _meetingService;
    private readonly GreenDisplayTemplateService _templateService;
    private readonly GreenDisplayAPService _apService;
    private readonly ThirdPartyApiService _thirdPartyApiService;
    private readonly ILogger<GreenDisplayController> _logger;

    public GreenDisplayController(
        GreenDisplayAuthService authService, 
        GreenDisplayLabelService labelService,
        GreenDisplayDeviceService deviceService,
        GreenDisplayMeetingService meetingService,
        GreenDisplayTemplateService templateService,
        GreenDisplayAPService apService,
        ThirdPartyApiService thirdPartyApiService,
        ILogger<GreenDisplayController> logger)
    {
        _authService = authService;
        _labelService = labelService;
        _deviceService = deviceService;
        _meetingService = meetingService;
        _templateService = templateService;
        _apService = apService;
        _thirdPartyApiService = thirdPartyApiService;
        _logger = logger;
    }

    /// <summary>
    /// 用户登录 🔐
    /// </summary>
    /// <param name="input">登录信息，包含用户名和密码</param>
    /// <returns>登录结果，包含访问令牌和用户信息</returns>
    /// <remarks>
    /// 示例请求：
    /// POST /admin-api/api/auth/login
    /// {
    ///   "username": "admin",
    ///   "password": "123456"
    /// }
    /// 
    /// 注意：此接口对接第三方平台，地址为 http://183.239.182.91:19080
    /// </remarks>
    [AllowAnonymous]
    [HttpPost(GreenDisplayConst.ApiRoutes.Auth.Login)]
    [DisplayName("用户登录")]
    public async Task<GreenDisplayLoginOutput> Login([FromBody] GreenDisplayLoginInput input)
    {
        return await _authService.Login(input);
    }

    /// <summary>
    /// 绑定多个标签 🏷️
    /// </summary>
    /// <param name="input">绑定参数，包含标签列表和绑定配置</param>
    /// <returns>绑定结果，返回成功绑定的标签数量和失败信息</returns>
    /// <remarks>
    /// 用于批量绑定电子标签到设备或位置
    /// 支持同时绑定多个标签，提高操作效率
    /// </remarks>
    [DisplayName("绑定多个标签")]
    [HttpPost(GreenDisplayConst.ApiRoutes.Label.BindMultiple)]
    public async Task<dynamic> BindMultipleLabels(BindMultipleLabelsInput input)
    {
        return await _labelService.BindMultipleLabels(input);
    }

    #region 设备管理

    /// <summary>
    /// 绑定多个标签数据 📊
    /// </summary>
    /// <param name="input">绑定参数，包含设备标识和数据内容</param>
    /// <returns>绑定结果，返回操作状态和绑定详情</returns>
    /// <remarks>
    /// 批量为电子标签绑定显示数据
    /// 支持文本、图片、二维码等多种数据类型
    /// </remarks>
    [DisplayName("绑定多个标签数据")]
    [HttpPost(GreenDisplayConst.ApiRoutes.Device.BindDataMultiple)]
    public async Task<dynamic> BindDataMultiple(BindDataMultiInput input)
    {
        return await _deviceService.BindDataMultiple(input);
    }

    /// <summary>
    /// 闪烁标签 💡
    /// </summary>
    /// <param name="input">闪烁参数，包含标签MAC地址和闪烁配置</param>
    /// <returns>操作结果，返回闪烁指令执行状态</returns>
    /// <remarks>
    /// 让指定的电子标签进行闪烁显示
    /// 用于设备定位和状态指示
    /// 可配置闪烁频率和持续时间
    /// </remarks>
    [DisplayName("闪烁标签")]
    [HttpPost(GreenDisplayConst.ApiRoutes.Device.FlashLabel)]
    public async Task<dynamic> FlashLabel(FlashLabelInput input)
    {
        return await _deviceService.FlashLabel(input);
    }

    /// <summary>
    /// 查询单个设备 🔍
    /// </summary>
    /// <param name="labelMac">标签MAC地址</param>
    /// <returns>设备信息</returns>
    [DisplayName("查询单个设备")]
    [HttpGet(GreenDisplayConst.ApiRoutes.Device.Get)]
    public async Task<dynamic> GetDevice(string labelMac)
    {
        return await _deviceService.GetDevice(labelMac);
    }

    /// <summary>
    /// 查询所有设备 📋
    /// </summary>
    /// <param name="input">查询参数</param>
    /// <returns>设备列表</returns>
    [DisplayName("查询所有设备")]
    [HttpGet(GreenDisplayConst.ApiRoutes.Device.Query)]
    public async Task<dynamic> QueryDevices([FromQuery] QueryLabelInput input)
    {
        return await _deviceService.QueryDevices(input);
    }

    /// <summary>
    /// 更新标签信息 ✏️
    /// </summary>
    /// <param name="input">更新参数</param>
    /// <returns>操作结果</returns>
    [DisplayName("更新标签信息")]
    [HttpPost(GreenDisplayConst.ApiRoutes.Device.UpdateLabel)]
    public async Task<dynamic> UpdateLabel(UpdateLabelInput input)
    {
        return await _deviceService.UpdateLabel(input);
    }

    #endregion

    #region 会议管理

    /// <summary>
    /// 查询会议室列表 🏢
    /// </summary>
    /// <param name="input">查询参数</param>
    /// <returns>会议室列表</returns>
    [DisplayName("查询会议室列表")]
    [HttpGet(GreenDisplayConst.ApiRoutes.Meeting.Room.Query)]
    public async Task<dynamic> QueryMeetingRooms([FromQuery] QueryMeetingRoomInput input)
    {
        return await _meetingService.QueryMeetingRooms(input);
    }

    /// <summary>
    /// 创建会议室 ➕
    /// </summary>
    /// <param name="input">创建参数</param>
    /// <returns>操作结果</returns>
    [DisplayName("创建会议室")]
    [HttpPost(GreenDisplayConst.ApiRoutes.Meeting.Room.Create)]
    public async Task<dynamic> CreateMeetingRoom(CreateMeetingRoomInput input)
    {
        return await _meetingService.CreateMeetingRoom(input);
    }

    /// <summary>
    /// 更新会议室 ✏️
    /// </summary>
    /// <param name="input">更新参数</param>
    /// <returns>操作结果</returns>
    [DisplayName("更新会议室")]
    [HttpPost(GreenDisplayConst.ApiRoutes.Meeting.Room.Update)]
    public async Task<dynamic> UpdateMeetingRoom(UpdateMeetingRoomInput input)
    {
        return await _meetingService.UpdateMeetingRoom(input);
    }

    /// <summary>
    /// 删除会议室 🗑️
    /// </summary>
    /// <param name="id">会议室ID</param>
    /// <returns>操作结果</returns>
    [DisplayName("删除会议室")]
    [HttpDelete(GreenDisplayConst.ApiRoutes.Meeting.Room.Delete)]
    public async Task<dynamic> DeleteMeetingRoom(long id)
    {
        return await _meetingService.DeleteMeetingRoom(id);
    }

    /// <summary>
    /// 查询会议人员列表 👥
    /// </summary>
    /// <param name="input">查询参数</param>
    /// <returns>人员列表</returns>
    [DisplayName("查询会议人员列表")]
    [HttpGet(GreenDisplayConst.ApiRoutes.Meeting.Staff.Query)]
    public async Task<dynamic> QueryMeetingStaff([FromQuery] QueryMeetingStaffInput input)
    {
        return await _meetingService.QueryMeetingStaff(input);
    }

    /// <summary>
    /// 创建会议人员 ➕
    /// </summary>
    /// <param name="input">创建参数</param>
    /// <returns>操作结果</returns>
    [DisplayName("创建会议人员")]
    [HttpPost(GreenDisplayConst.ApiRoutes.Meeting.Staff.Create)]
    public async Task<dynamic> CreateMeetingStaff(CreateMeetingStaffInput input)
    {
        return await _meetingService.CreateMeetingStaff(input);
    }

    /// <summary>
    /// 批量创建会议人员 📝
    /// </summary>
    /// <param name="input">批量创建参数</param>
    /// <returns>操作结果</returns>
    [DisplayName("批量创建会议人员")]
    [HttpPost(GreenDisplayConst.ApiRoutes.Meeting.Staff.BatchCreate)]
    public async Task<dynamic> BatchCreateMeetingStaff(BatchCreateMeetingStaffInput input)
    {
        return await _meetingService.BatchCreateMeetingStaff(input);
    }

    /// <summary>
    /// 更新会议人员 ✏️
    /// </summary>
    /// <param name="input">更新参数</param>
    /// <returns>操作结果</returns>
    [DisplayName("更新会议人员")]
    [HttpPost(GreenDisplayConst.ApiRoutes.Meeting.Staff.Update)]
    public async Task<dynamic> UpdateMeetingStaff(UpdateMeetingStaffInput input)
    {
        return await _meetingService.UpdateMeetingStaff(input);
    }

    /// <summary>
    /// 批量更新会议人员 📝
    /// </summary>
    /// <param name="input">批量更新参数</param>
    /// <returns>操作结果</returns>
    [DisplayName("批量更新会议人员")]
    [HttpPost(GreenDisplayConst.ApiRoutes.Meeting.Staff.BatchUpdate)]
    public async Task<dynamic> BatchUpdateMeetingStaff(BatchUpdateMeetingStaffInput input)
    {
        return await _meetingService.BatchUpdateMeetingStaff(input);
    }

    /// <summary>
    /// 删除会议人员 🗑️
    /// </summary>
    /// <param name="id">人员ID</param>
    /// <returns>操作结果</returns>
    [DisplayName("删除会议人员")]
    [HttpDelete(GreenDisplayConst.ApiRoutes.Meeting.Staff.Delete)]
    public async Task<dynamic> DeleteMeetingStaff(long id)
    {
        return await _meetingService.DeleteMeetingStaff(id);
    }

    #endregion

    #region 模板管理

    /// <summary>
    /// 查询模板列表 📄
    /// </summary>
    /// <param name="input">查询参数</param>
    /// <returns>模板列表</returns>
    [DisplayName("查询模板列表")]
    [HttpGet(GreenDisplayConst.ApiRoutes.Template.Query)]
    public async Task<dynamic> QueryTemplates([FromQuery] QueryTemplateInput input)
    {
        return await _templateService.QueryTemplates(input);
    }

    /// <summary>
    /// 创建模板 ➕
    /// </summary>
    /// <param name="input">创建参数</param>
    /// <returns>操作结果</returns>
    [DisplayName("创建模板")]
    [HttpPost(GreenDisplayConst.ApiRoutes.Template.Create)]
    public async Task<dynamic> CreateTemplate(CreateTemplateInput input)
    {
        return await _templateService.CreateTemplate(input);
    }

    /// <summary>
    /// 获取模板设计 🎨
    /// </summary>
    /// <param name="id">模板ID</param>
    /// <returns>模板设计信息</returns>
    [DisplayName("获取模板设计")]
    [HttpGet(GreenDisplayConst.ApiRoutes.Template.Design)]
    public async Task<dynamic> GetTemplateDesign(long id)
    {
        return await _templateService.GetTemplateDesign(id);
    }

    /// <summary>
    /// 更新模板 ✏️
    /// </summary>
    /// <param name="input">更新参数</param>
    /// <returns>操作结果</returns>
    [DisplayName("更新模板")]
    [HttpPost(GreenDisplayConst.ApiRoutes.Template.Update)]
    public async Task<dynamic> UpdateTemplate(Service.UpdateTemplateInput input)
    {
        return await _templateService.UpdateTemplate(input);
    }

    /// <summary>
    /// 删除模板 🗑️
    /// </summary>
    /// <param name="id">模板ID</param>
    /// <returns>操作结果</returns>
    [DisplayName("删除模板")]
    [HttpDelete(GreenDisplayConst.ApiRoutes.Template.Delete)]
    public async Task<dynamic> DeleteTemplate(long id)
    {
        return await _templateService.DeleteTemplate(id);
    }

    #endregion

    #region AP管理

    /// <summary>
    /// 获取AP列表 📡
    /// </summary>
    /// <param name="input">查询参数</param>
    /// <returns>AP列表</returns>
    [DisplayName("获取AP列表")]
    [HttpGet(GreenDisplayConst.ApiRoutes.AP.List)]
    public async Task<dynamic> GetAPList([FromQuery] QueryAPInput input)
    {
        return await _apService.GetAPList(input);
    }

    /// <summary>
    /// 创建AP ➕
    /// </summary>
    /// <param name="input">创建参数</param>
    /// <returns>操作结果</returns>
    [DisplayName("创建AP")]
    [HttpPost(GreenDisplayConst.ApiRoutes.AP.Create)]
    public async Task<dynamic> CreateAP(CreateAPInput input)
    {
        return await _apService.CreateAP(input);
    }

    /// <summary>
    /// 获取单个AP 🔍
    /// </summary>
    /// <param name="apMac">AP MAC地址</param>
    /// <returns>AP信息</returns>
    [DisplayName("获取单个AP")]
    [HttpGet(GreenDisplayConst.ApiRoutes.AP.Get)]
    public async Task<dynamic> GetAP(string apMac)
    {
        return await _apService.GetAP(apMac);
    }

    /// <summary>
    /// 更新AP ✏️
    /// </summary>
    /// <param name="input">更新参数</param>
    /// <returns>操作结果</returns>
    [DisplayName("更新AP")]
    [HttpPost(GreenDisplayConst.ApiRoutes.AP.Update)]
    public async Task<dynamic> UpdateAP(UpdateAPInput input)
    {
        return await _apService.UpdateAP(input);
    }

    /// <summary>
    /// 删除AP 🗑️
    /// </summary>
    /// <param name="apMac">AP MAC地址</param>
    /// <returns>操作结果</returns>
    [DisplayName("删除AP")]
    [HttpDelete(GreenDisplayConst.ApiRoutes.AP.Delete)]
    public async Task<dynamic> DeleteAP(string apMac)
    {
        return await _apService.DeleteAP(apMac);
    }

    #endregion
}