// Admin.NET 项目的版权、商标、专利和其他相关权利均受相应法律法规的保护。使用本项目应遵守相关法律法规和许可证的要求。
//
// 本项目主要遵循 MIT 许可证和 Apache 许可证（版本 2.0）进行分发和使用。许可证位于源代码树根目录中的 LICENSE-MIT 和 LICENSE-APACHE 文件。
//
// 不得利用本项目从事危害国家安全、扰乱社会秩序、侵犯他人合法权益等法律法规禁止的活动！任何基于本项目二次开发而产生的一切法律纠纷和责任，我们不承担任何责任！

using Admin.NET.Plugin.GreenDisplay.Option;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Options;
using System.Net.Http.Headers;
using System.Text;
using System.Text.Json;

namespace Admin.NET.Plugin.GreenDisplay.Service;

/// <summary>
/// 第三方API服务
/// </summary>
public class ThirdPartyApiService : ITransient
{
    private readonly HttpClient _httpClient;
    private readonly ThirdPartyApiOptions _options;
    private readonly ILogger<ThirdPartyApiService> _logger;

    public ThirdPartyApiService(
        HttpClient httpClient,
        IOptions<ThirdPartyApiOptions> options,
        ILogger<ThirdPartyApiService> logger)
    {
        _httpClient = httpClient;
        _options = options.Value;
        _logger = logger;

        // 配置HttpClient
        _httpClient.Timeout = TimeSpan.FromMilliseconds(_options.Timeout);
    }

    /// <summary>
    /// 发送GET请求
    /// </summary>
    /// <typeparam name="T">返回类型</typeparam>
    /// <param name="route">路由路径</param>
    /// <param name="queryParams">查询参数</param>
    /// <returns>响应结果</returns>
    public async Task<T> GetAsync<T>(string route, object queryParams = null)
    {
        var url = BuildUrl(route, queryParams);
        return await SendRequestAsync<T>(HttpMethod.Get, url);
    }

    /// <summary>
    /// 发送POST请求
    /// </summary>
    /// <typeparam name="T">返回类型</typeparam>
    /// <param name="route">路由路径</param>
    /// <param name="data">请求数据</param>
    /// <returns>响应结果</returns>
    public async Task<T> PostAsync<T>(string route, object data = null)
    {
        var url = _options.GetFullUrl(route);
        return await SendRequestAsync<T>(HttpMethod.Post, url, data);
    }

    /// <summary>
    /// 发送DELETE请求
    /// </summary>
    /// <typeparam name="T">返回类型</typeparam>
    /// <param name="route">路由路径</param>
    /// <param name="queryParams">查询参数</param>
    /// <returns>响应结果</returns>
    public async Task<T> DeleteAsync<T>(string route, object queryParams = null)
    {
        var url = BuildUrl(route, queryParams);
        return await SendRequestAsync<T>(HttpMethod.Delete, url);
    }

    /// <summary>
    /// 构建URL
    /// </summary>
    /// <param name="route">路由路径</param>
    /// <param name="queryParams">查询参数</param>
    /// <returns>完整URL</returns>
    private string BuildUrl(string route, object queryParams)
    {
        var url = _options.GetFullUrl(route);
        
        if (queryParams != null)
        {
            var queryString = BuildQueryString(queryParams);
            if (!string.IsNullOrEmpty(queryString))
            {
                url += $"?{queryString}";
            }
        }
        
        return url;
    }

    /// <summary>
    /// 构建查询字符串
    /// </summary>
    /// <param name="queryParams">查询参数</param>
    /// <returns>查询字符串</returns>
    private string BuildQueryString(object queryParams)
    {
        var properties = queryParams.GetType().GetProperties();
        var queryParts = new List<string>();

        foreach (var property in properties)
        {
            var value = property.GetValue(queryParams);
            if (value != null)
            {
                queryParts.Add($"{property.Name}={Uri.EscapeDataString(value.ToString())}");
            }
        }

        return string.Join("&", queryParts);
    }

    /// <summary>
    /// 发送HTTP请求
    /// </summary>
    /// <typeparam name="T">返回类型</typeparam>
    /// <param name="method">HTTP方法</param>
    /// <param name="url">请求URL</param>
    /// <param name="data">请求数据</param>
    /// <returns>响应结果</returns>
    private async Task<T> SendRequestAsync<T>(HttpMethod method, string url, object data = null)
    {
        var retryCount = 0;
        Exception lastException = null;

        while (retryCount <= _options.RetryCount)
        {
            try
            {
                using var request = new HttpRequestMessage(method, url);

                // 添加请求体
                if (data != null && (method == HttpMethod.Post || method == HttpMethod.Put))
                {
                    var json = System.Text.Json.JsonSerializer.Serialize(data);
                    request.Content = new StringContent(json, Encoding.UTF8, MediaTypeHeaderValue.Parse("application/json"));
                }

                // 记录请求日志
                if (_options.EnableLogging)
                {
                    _logger.LogInformation("发送HTTP请求: {Method} {Url}", method, url);
                    if (data != null)
                    {
                        _logger.LogDebug("请求数据: {Data}", System.Text.Json.JsonSerializer.Serialize(data));
                    }
                }

                // 发送请求
                using var response = await _httpClient.SendAsync(request);
                var responseContent = await response.Content.ReadAsStringAsync();

                // 记录响应日志
                if (_options.EnableLogging)
                {
                    _logger.LogInformation("收到HTTP响应: {StatusCode}", response.StatusCode);
                    _logger.LogDebug("响应内容: {Content}", responseContent);
                }

                // 检查响应状态
                if (response.IsSuccessStatusCode)
                {
                    return System.Text.Json.JsonSerializer.Deserialize<T>(responseContent, new JsonSerializerOptions
                    {
                        PropertyNameCaseInsensitive = true
                    });
                }
                else
                {
                    throw new HttpRequestException($"HTTP请求失败: {response.StatusCode} - {responseContent}");
                }
            }
            catch (Exception ex)
            {
                lastException = ex;
                retryCount++;

                if (retryCount <= _options.RetryCount)
                {
                    var delay = TimeSpan.FromSeconds(Math.Pow(2, retryCount)); // 指数退避
                    _logger.LogWarning("HTTP请求失败，{Delay}秒后重试 (第{RetryCount}次): {Error}", delay.TotalSeconds, retryCount, ex.Message);
                    await Task.Delay(delay);
                }
            }
        }

        _logger.LogError(lastException, "HTTP请求最终失败，已达到最大重试次数: {MaxRetry}", _options.RetryCount);
        throw lastException ?? new HttpRequestException("HTTP请求失败");
    }
}